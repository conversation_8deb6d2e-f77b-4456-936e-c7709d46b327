# Deployment Guide

## Overview
This document provides comprehensive deployment instructions for the grep.app code search engine.

## Prerequisites
- Node.js 18+ 
- Docker and Docker Compose
- Git

## Local Development

### Quick Start
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:3000
```

### Development with Docker
```bash
# Build and run development environment
docker-compose --profile dev up -d

# Access at http://localhost:3001
```

## Production Deployment

### Docker Production Build
```bash
# Build production image
docker build -t grep-app .

# Run production container
docker run -d -p 3000:3000 --name grep-app grep-app

# Or use docker-compose
docker-compose up -d
```

### Static Export for CDN/Static Hosting
```bash
# Build static export
npm run build

# Serve static files (out/ directory)
npx serve out
```

## Vercel Deployment

### Configuration
The application is configured for Vercel deployment with:
- Static export enabled (`output: 'export'`)
- Client-side search service
- Optimized build settings

### Deploy Steps
1. Connect repository to Vercel
2. Configure build settings:
   - Build Command: `npm run build`
   - Output Directory: `out`
   - Install Command: `npm ci`

3. Environment Variables: None required (static export)

### Vercel Configuration File
Create `vercel.json`:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "out",
  "framework": "nextjs",
  "functions": {},
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## Performance Optimizations

### Build Optimizations
- Static export for CDN distribution
- Font optimization with Geist Sans/JetBrains Mono
- CSS optimization with Tailwind purging
- Component code splitting

### Runtime Optimizations
- Debounced search (500ms)
- Client-side caching with SWR
- Lazy loading for search results
- Optimized bundle sizes

## Monitoring and Health Checks

### Docker Health Check
```bash
# Check container health
docker ps
curl -f http://localhost:3000 || echo "Health check failed"
```

### Application Monitoring
- Monitor search response times
- Track error rates
- Monitor bundle sizes
- Performance metrics via Web Vitals

## Troubleshooting

### Common Issues
1. **Port conflicts**: Use different ports in docker-compose.yml
2. **Build failures**: Ensure all dependencies are installed
3. **Static export issues**: Verify no server-side features are used

### Debug Commands
```bash
# Check container logs
docker logs grep-app

# Debug build process
npm run build -- --debug

# Test production build locally
npm run start
```

## Security Considerations
- No server-side code (static export)
- CSP headers for XSS protection
- Secure font loading
- No sensitive data exposure

## Scaling Considerations
- CDN distribution for global performance
- Client-side search scales with user devices
- Consider search API for large datasets
- Monitor client-side performance
