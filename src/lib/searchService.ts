import { SearchResult } from '@/components/ResultCard';

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  took: number;
}

// Mock data for demonstration
const MOCK_RESULTS: SearchResult[] = [
  {
    id: '1',
    filename: 'useState.ts',
    path: 'packages/react/src/ReactHooks.ts',
    repository: {
      name: 'facebook/react',
      url: 'https://github.com/facebook/react',
      stars: 228000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 123,
        content: 'export function useState<S>(initialState: S | (() => S)): [S, Dispatch<SetStateAction<S>>] {',
        highlighted: 'export function <mark>useState</mark>&lt;S&gt;(initialState: S | (() =&gt; S)): [S, Dispatch&lt;SetStateAction&lt;S&gt;&gt;] {'
      },
      {
        lineNumber: 124,
        content: '  return resolveDispatcher().useState(initialState);',
        highlighted: '  return resolveDispatcher().<mark>useState</mark>(initialState);'
      },
      {
        lineNumber: 125,
        content: '}',
      }
    ],
    url: 'https://github.com/facebook/react/blob/main/packages/react/src/ReactHooks.ts#L123'
  },
  {
    id: '2',
    filename: 'hooks.js',
    path: 'src/components/Counter.js',
    repository: {
      name: 'vercel/next.js',
      url: 'https://github.com/vercel/next.js',
      stars: 125000,
      language: 'JavaScript'
    },
    matches: [
      {
        lineNumber: 5,
        content: 'import { useState, useEffect } from "react";',
        highlighted: 'import { <mark>useState</mark>, useEffect } from "react";'
      },
      {
        lineNumber: 8,
        content: 'const [count, setCount] = useState(0);',
        highlighted: 'const [count, setCount] = <mark>useState</mark>(0);'
      }
    ],
    url: 'https://github.com/vercel/next.js/blob/canary/examples/with-react-hooks/components/Counter.js#L5'
  },
  {
    id: '3',
    filename: 'App.tsx',
    path: 'src/App.tsx',
    repository: {
      name: 'microsoft/vscode',
      url: 'https://github.com/microsoft/vscode',
      stars: 163000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 12,
        content: 'const [theme, setTheme] = useState("dark");',
        highlighted: 'const [theme, setTheme] = <mark>useState</mark>("dark");'
      },
      {
        lineNumber: 15,
        content: 'const [isLoading, setIsLoading] = useState(false);',
        highlighted: 'const [isLoading, setIsLoading] = <mark>useState</mark>(false);'
      }
    ],
    url: 'https://github.com/microsoft/vscode/blob/main/src/vs/workbench/browser/workbench.ts#L12'
  }
];

function generateMoreResults(query: string, count: number): SearchResult[] {
  const templates = [
    {
      filename: 'component.tsx',
      path: 'src/components/Component.tsx',
      repository: { name: 'example/repo', url: 'https://github.com/example/repo', stars: 1200, language: 'TypeScript' }
    },
    {
      filename: 'utils.js',
      path: 'src/utils/helpers.js',
      repository: { name: 'awesome/project', url: 'https://github.com/awesome/project', stars: 850, language: 'JavaScript' }
    },
    {
      filename: 'main.py',
      path: 'src/main.py',
      repository: { name: 'python/example', url: 'https://github.com/python/example', stars: 2300, language: 'Python' }
    }
  ];

  return Array.from({ length: count }, (_, i) => {
    const template = templates[i % templates.length];
    return {
      id: `generated-${i}`,
      filename: template.filename,
      path: template.path,
      repository: template.repository,
      matches: [
        {
          lineNumber: 10 + i,
          content: `function example() { return ${query}; }`,
          highlighted: `function example() { return <mark>${query}</mark>; }`
        }
      ],
      url: `${template.repository.url}/blob/main/${template.path}#L${10 + i}`
    };
  });
}

export async function searchCode(params: URLSearchParams): Promise<SearchResponse> {
  const query = params.get('q');
  const languages = params.getAll('lang');
  const repositories = params.getAll('repo');

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

  if (!query) {
    return { 
      results: [], 
      total: 0, 
      query: '', 
      took: 0 
    };
  }

  // Filter mock results based on query and filters
  let results = MOCK_RESULTS.filter(result => 
    result.matches.some(match => 
      match.content.toLowerCase().includes(query.toLowerCase())
    )
  );

  // Apply language filter
  if (languages.length > 0) {
    results = results.filter(result => 
      result.repository.language && 
      languages.includes(result.repository.language)
    );
  }

  // Apply repository filter
  if (repositories.length > 0) {
    results = results.filter(result => 
      repositories.includes(result.repository.name)
    );
  }

  // Generate additional results if needed
  if (results.length < 10) {
    const additionalResults = generateMoreResults(query, 10 - results.length);
    // Apply filters to generated results as well
    let filteredAdditionalResults = additionalResults;

    if (languages.length > 0) {
      filteredAdditionalResults = filteredAdditionalResults.filter(result =>
        result.repository.language &&
        languages.includes(result.repository.language)
      );
    }

    if (repositories.length > 0) {
      filteredAdditionalResults = filteredAdditionalResults.filter(result =>
        repositories.includes(result.repository.name)
      );
    }

    results = [...results, ...filteredAdditionalResults];
  }

  // Simulate highlighting for the query
  results = results.map(result => ({
    ...result,
    matches: result.matches.map(match => ({
      ...match,
      highlighted: match.highlighted || match.content.replace(
        new RegExp(`(${query})`, 'gi'),
        '<mark>$1</mark>'
      )
    }))
  }));

  return {
    results,
    total: results.length + Math.floor(Math.random() * 10000),
    query,
    took: Math.floor(Math.random() * 50) + 10
  };
}
