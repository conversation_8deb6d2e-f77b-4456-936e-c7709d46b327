import { SearchResult } from '@/components/ResultCard';

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  took: number;
}

// Mock data for demonstration - 丰富的模拟数据
const MOCK_RESULTS: SearchResult[] = [
  {
    id: '1',
    filename: 'ReactHooks.ts',
    path: 'packages/react/src/ReactHooks.ts',
    repository: {
      name: 'facebook/react',
      url: 'https://github.com/facebook/react',
      stars: 228000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 123,
        content: 'export function useState<S>(initialState: S | (() => S)): [S, Dispatch<SetStateAction<S>>] {',
        highlighted: 'export function <mark>useState</mark>&lt;S&gt;(initialState: S | (() =&gt; S)): [S, Dispatch&lt;SetStateAction&lt;S&gt;&gt;] {'
      },
      {
        lineNumber: 124,
        content: '  return resolveDispatcher().useState(initialState);',
        highlighted: '  return resolveDispatcher().<mark>useState</mark>(initialState);'
      },
      {
        lineNumber: 125,
        content: '}',
      }
    ],
    url: 'https://github.com/facebook/react/blob/main/packages/react/src/ReactHooks.ts#L123'
  },
  {
    id: '2',
    filename: 'Counter.js',
    path: 'examples/with-react-hooks/components/Counter.js',
    repository: {
      name: 'vercel/next.js',
      url: 'https://github.com/vercel/next.js',
      stars: 125000,
      language: 'JavaScript'
    },
    matches: [
      {
        lineNumber: 5,
        content: 'import { useState, useEffect } from "react";',
        highlighted: 'import { <mark>useState</mark>, useEffect } from "react";'
      },
      {
        lineNumber: 8,
        content: 'const [count, setCount] = useState(0);',
        highlighted: 'const [count, setCount] = <mark>useState</mark>(0);'
      },
      {
        lineNumber: 12,
        content: 'const [isVisible, setIsVisible] = useState(true);',
        highlighted: 'const [isVisible, setIsVisible] = <mark>useState</mark>(true);'
      }
    ],
    url: 'https://github.com/vercel/next.js/blob/canary/examples/with-react-hooks/components/Counter.js#L5'
  },
  {
    id: '3',
    filename: 'workbench.ts',
    path: 'src/vs/workbench/browser/workbench.ts',
    repository: {
      name: 'microsoft/vscode',
      url: 'https://github.com/microsoft/vscode',
      stars: 163000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 12,
        content: 'const [theme, setTheme] = useState("dark");',
        highlighted: 'const [theme, setTheme] = <mark>useState</mark>("dark");'
      },
      {
        lineNumber: 15,
        content: 'const [isLoading, setIsLoading] = useState(false);',
        highlighted: 'const [isLoading, setIsLoading] = <mark>useState</mark>(false);'
      }
    ],
    url: 'https://github.com/microsoft/vscode/blob/main/src/vs/workbench/browser/workbench.ts#L12'
  },
  {
    id: '4',
    filename: 'useAuth.ts',
    path: 'src/hooks/useAuth.ts',
    repository: {
      name: 'supabase/supabase',
      url: 'https://github.com/supabase/supabase',
      stars: 72000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 18,
        content: 'const [user, setUser] = useState<User | null>(null);',
        highlighted: 'const [user, setUser] = <mark>useState</mark>&lt;User | null&gt;(null);'
      },
      {
        lineNumber: 19,
        content: 'const [loading, setLoading] = useState(true);',
        highlighted: 'const [loading, setLoading] = <mark>useState</mark>(true);'
      },
      {
        lineNumber: 20,
        content: 'const [error, setError] = useState<string | null>(null);',
        highlighted: 'const [error, setError] = <mark>useState</mark>&lt;string | null&gt;(null);'
      }
    ],
    url: 'https://github.com/supabase/supabase/blob/master/examples/nextjs/hooks/useAuth.ts#L18'
  },
  {
    id: '5',
    filename: 'TodoApp.jsx',
    path: 'src/components/TodoApp.jsx',
    repository: {
      name: 'reduxjs/redux',
      url: 'https://github.com/reduxjs/redux',
      stars: 60500,
      language: 'JavaScript'
    },
    matches: [
      {
        lineNumber: 25,
        content: 'const [todos, setTodos] = useState([]);',
        highlighted: 'const [todos, setTodos] = <mark>useState</mark>([]);'
      },
      {
        lineNumber: 26,
        content: 'const [inputValue, setInputValue] = useState("");',
        highlighted: 'const [inputValue, setInputValue] = <mark>useState</mark>("");'
      },
      {
        lineNumber: 35,
        content: 'const [filter, setFilter] = useState("all");',
        highlighted: 'const [filter, setFilter] = <mark>useState</mark>("all");'
      }
    ],
    url: 'https://github.com/reduxjs/redux/blob/master/examples/todos-with-undo/src/components/TodoApp.jsx#L25'
  },
  {
    id: '6',
    filename: 'SearchBox.tsx',
    path: 'packages/react-instantsearch/src/widgets/SearchBox.tsx',
    repository: {
      name: 'algolia/instantsearch',
      url: 'https://github.com/algolia/instantsearch',
      stars: 3200,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 42,
        content: 'const [query, setQuery] = useState(props.defaultRefinement || "");',
        highlighted: 'const [query, setQuery] = <mark>useState</mark>(props.defaultRefinement || "");'
      },
      {
        lineNumber: 43,
        content: 'const [isFocused, setIsFocused] = useState(false);',
        highlighted: 'const [isFocused, setIsFocused] = <mark>useState</mark>(false);'
      }
    ],
    url: 'https://github.com/algolia/instantsearch/blob/master/packages/react-instantsearch/src/widgets/SearchBox.tsx#L42'
  },
  {
    id: '7',
    filename: 'useLocalStorage.ts',
    path: 'src/hooks/useLocalStorage.ts',
    repository: {
      name: 'streamich/react-use',
      url: 'https://github.com/streamich/react-use',
      stars: 41000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 15,
        content: 'const [storedValue, setStoredValue] = useState<T>(() => {',
        highlighted: 'const [storedValue, setStoredValue] = <mark>useState</mark>&lt;T&gt;(() =&gt; {'
      },
      {
        lineNumber: 25,
        content: 'const [value, setValue] = useState(storedValue);',
        highlighted: 'const [value, setValue] = <mark>useState</mark>(storedValue);'
      }
    ],
    url: 'https://github.com/streamich/react-use/blob/master/src/useLocalStorage.ts#L15'
  },
  {
    id: '8',
    filename: 'Modal.tsx',
    path: 'src/components/ui/Modal.tsx',
    repository: {
      name: 'chakra-ui/chakra-ui',
      url: 'https://github.com/chakra-ui/chakra-ui',
      stars: 37000,
      language: 'TypeScript'
    },
    matches: [
      {
        lineNumber: 28,
        content: 'const [isOpen, setIsOpen] = useState(defaultIsOpen);',
        highlighted: 'const [isOpen, setIsOpen] = <mark>useState</mark>(defaultIsOpen);'
      },
      {
        lineNumber: 29,
        content: 'const [isAnimating, setIsAnimating] = useState(false);',
        highlighted: 'const [isAnimating, setIsAnimating] = <mark>useState</mark>(false);'
      }
    ],
    url: 'https://github.com/chakra-ui/chakra-ui/blob/main/packages/components/modal/src/modal.tsx#L28'
  }
];

function generateMoreResults(query: string, count: number): SearchResult[] {
  const templates = [
    {
      filename: 'Button.tsx',
      path: 'src/components/ui/Button.tsx',
      repository: { name: 'shadcn-ui/ui', url: 'https://github.com/shadcn-ui/ui', stars: 71000, language: 'TypeScript' },
      codeTemplates: [
        `const [isLoading, setIsLoading] = ${query}(false);`,
        `export const Button = ({ children, ${query}, ...props }) => {`,
        `// ${query} implementation for button component`
      ]
    },
    {
      filename: 'api.js',
      path: 'src/utils/api.js',
      repository: { name: 'axios/axios', url: 'https://github.com/axios/axios', stars: 105000, language: 'JavaScript' },
      codeTemplates: [
        `async function fetch${query}() {`,
        `const ${query}Config = { timeout: 5000 };`,
        `// Handle ${query} response`
      ]
    },
    {
      filename: 'server.py',
      path: 'src/server.py',
      repository: { name: 'pallets/flask', url: 'https://github.com/pallets/flask', stars: 67000, language: 'Python' },
      codeTemplates: [
        `def handle_${query}(request):`,
        `@app.route('/${query}', methods=['GET', 'POST'])`,
        `# ${query} endpoint implementation`
      ]
    },
    {
      filename: 'database.go',
      path: 'internal/database/database.go',
      repository: { name: 'golang/go', url: 'https://github.com/golang/go', stars: 123000, language: 'Go' },
      codeTemplates: [
        `func Query${query}(ctx context.Context) error {`,
        `type ${query}Model struct {`,
        `// ${query} database operations`
      ]
    },
    {
      filename: 'styles.css',
      path: 'src/styles/components.css',
      repository: { name: 'tailwindlabs/tailwindcss', url: 'https://github.com/tailwindlabs/tailwindcss', stars: 82000, language: 'CSS' },
      codeTemplates: [
        `.${query} { display: flex; }`,
        `/* ${query} component styles */`,
        `.${query}-container { width: 100%; }`
      ]
    },
    {
      filename: 'config.json',
      path: 'config/app.json',
      repository: { name: 'microsoft/TypeScript', url: 'https://github.com/microsoft/TypeScript', stars: 100000, language: 'JSON' },
      codeTemplates: [
        `"${query}": true,`,
        `"${query}Config": { "enabled": true },`,
        `// ${query} configuration`
      ]
    }
  ];

  return Array.from({ length: count }, (_, i) => {
    const template = templates[i % templates.length];
    const codeTemplate = template.codeTemplates[i % template.codeTemplates.length];
    const lineNumber = 10 + (i * 3) + Math.floor(Math.random() * 20);

    return {
      id: `generated-${i}`,
      filename: template.filename,
      path: template.path,
      repository: template.repository,
      matches: [
        {
          lineNumber: lineNumber,
          content: codeTemplate,
          highlighted: codeTemplate.replace(
            new RegExp(`(${query})`, 'gi'),
            '<mark>$1</mark>'
          )
        },
        {
          lineNumber: lineNumber + 1,
          content: `  // Additional context for ${query}`,
          highlighted: `  // Additional context for <mark>${query}</mark>`
        }
      ],
      url: `${template.repository.url}/blob/main/${template.path}#L${lineNumber}`
    };
  });
}

export async function searchCode(params: URLSearchParams): Promise<SearchResponse> {
  const query = params.get('q');
  const languages = params.getAll('lang');
  const repositories = params.getAll('repo');

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

  if (!query) {
    return { 
      results: [], 
      total: 0, 
      query: '', 
      took: 0 
    };
  }

  // Filter mock results based on query and filters
  let results = MOCK_RESULTS.filter(result => 
    result.matches.some(match => 
      match.content.toLowerCase().includes(query.toLowerCase())
    )
  );

  // Apply language filter
  if (languages.length > 0) {
    results = results.filter(result => 
      result.repository.language && 
      languages.includes(result.repository.language)
    );
  }

  // Apply repository filter
  if (repositories.length > 0) {
    results = results.filter(result => 
      repositories.includes(result.repository.name)
    );
  }

  // Generate additional results if needed
  if (results.length < 10) {
    const additionalResults = generateMoreResults(query, 10 - results.length);
    // Apply filters to generated results as well
    let filteredAdditionalResults = additionalResults;

    if (languages.length > 0) {
      filteredAdditionalResults = filteredAdditionalResults.filter(result =>
        result.repository.language &&
        languages.includes(result.repository.language)
      );
    }

    if (repositories.length > 0) {
      filteredAdditionalResults = filteredAdditionalResults.filter(result =>
        repositories.includes(result.repository.name)
      );
    }

    results = [...results, ...filteredAdditionalResults];
  }

  // Simulate highlighting for the query
  results = results.map(result => ({
    ...result,
    matches: result.matches.map(match => ({
      ...match,
      highlighted: match.highlighted || match.content.replace(
        new RegExp(`(${query})`, 'gi'),
        '<mark>$1</mark>'
      )
    }))
  }));

  return {
    results,
    total: results.length + Math.floor(Math.random() * 10000),
    query,
    took: Math.floor(Math.random() * 50) + 10
  };
}
