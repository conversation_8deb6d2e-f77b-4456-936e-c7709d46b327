'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDebounce } from 'use-debounce';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  className?: string;
  placeholder?: string;
  size?: 'sm' | 'md' | 'lg';
  autoFocus?: boolean;
  onSearch?: (query: string) => void;
}

export function SearchInput({
  className,
  placeholder = "Search code...",
  size = 'md',
  autoFocus = false,
  onSearch
}: SearchInputProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 使用 ref 来跟踪是否是程序化更新
  const isUpdatingFromUrl = useRef(false);

  // 初始化本地状态
  const [text, setText] = useState(() => searchParams.get('q') || '');

  // 防抖处理用户输入
  const [debouncedValue] = useDebounce(text, 500);

  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  };

  // 监听 URL 参数变化，同步到本地状态（浏览器前进后退）
  useEffect(() => {
    const currentQuery = searchParams.get('q') || '';

    // 只有当 URL 参数与本地状态不同，且不是程序化更新时才更新本地状态
    if (currentQuery !== text && !isUpdatingFromUrl.current) {
      setText(currentQuery);
    }

    // 重置标志
    isUpdatingFromUrl.current = false;
  }, [searchParams, text]);

  // 监听防抖后的值变化，更新 URL
  useEffect(() => {
    const currentQuery = searchParams.get('q') || '';

    // 只有当防抖值与当前 URL 参数不同时才更新 URL
    if (debouncedValue !== currentQuery) {
      // 设置标志，表示这是程序化更新
      isUpdatingFromUrl.current = true;

      if (debouncedValue.trim()) {
        const newParams = new URLSearchParams(searchParams);
        newParams.set('q', debouncedValue.trim());
        router.push(`/search?${newParams.toString()}`);
      } else if (currentQuery) {
        // 如果搜索被清空，回到首页
        router.push('/');
      }

      // 调用回调函数
      onSearch?.(debouncedValue.trim());
    }
  }, [debouncedValue, router, searchParams, onSearch]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      isUpdatingFromUrl.current = true;
      const newParams = new URLSearchParams(searchParams);
      newParams.set('q', text.trim());
      router.push(`/search?${newParams.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          autoFocus={autoFocus}
          className={cn(
            'pl-10 pr-4 border-border focus:ring-2 focus:ring-ring focus:border-transparent',
            sizeClasses[size],
            className
          )}
        />
      </div>
    </form>
  );
}
