'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDebounce } from 'use-debounce';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  className?: string;
  placeholder?: string;
  size?: 'sm' | 'md' | 'lg';
  autoFocus?: boolean;
  onSearch?: (query: string) => void;
}

export function SearchInput({
  className,
  placeholder = "Search code...",
  size = 'md',
  autoFocus = false,
  onSearch
}: SearchInputProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 初始化本地状态 - 从 URL 参数获取初始值
  const [text, setText] = useState(() => searchParams.get('q') || '');

  // 防抖处理用户输入
  const [debouncedValue] = useDebounce(text, 500);

  // 用于跟踪上一次的 searchParams，避免不必要的更新
  const prevSearchParamsRef = useRef(searchParams.get('q') || '');

  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  };

  // 监听 URL 参数变化，同步到本地状态（主要用于浏览器前进后退）
  useEffect(() => {
    const currentQuery = searchParams.get('q') || '';
    const prevQuery = prevSearchParamsRef.current;

    // 只有当 URL 参数真正发生变化时才更新本地状态
    // 这避免了由于 router.push 导致的循环更新
    if (currentQuery !== prevQuery) {
      setText(currentQuery);
      prevSearchParamsRef.current = currentQuery;
    }
  }, [searchParams]);

  // 监听防抖后的值变化，更新 URL
  useEffect(() => {
    const currentQuery = searchParams.get('q') || '';

    // 只有当防抖值与当前 URL 参数不同时才更新 URL
    if (debouncedValue !== currentQuery) {
      if (debouncedValue.trim()) {
        // 有搜索内容，跳转到搜索页面
        const newParams = new URLSearchParams(searchParams);
        newParams.set('q', debouncedValue.trim());
        router.push(`/search?${newParams.toString()}`);
      } else if (currentQuery) {
        // 搜索被清空，回到首页
        router.push('/');
      }

      // 调用回调函数
      onSearch?.(debouncedValue.trim());
    }
  }, [debouncedValue, router, searchParams, onSearch]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('q', text.trim());
      router.push(`/search?${newParams.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          autoFocus={autoFocus}
          className={cn(
            'pl-10 pr-4 border-border focus:ring-2 focus:ring-ring focus:border-transparent',
            sizeClasses[size],
            className
          )}
        />
      </div>
    </form>
  );
}
