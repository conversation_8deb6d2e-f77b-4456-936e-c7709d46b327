import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';

export function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <Card key={i} className="result-card">
          <div className="space-y-3">
            {/* Header with file info - 匹配 ResultCard 的布局 */}
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                {/* File path with language badge */}
                <div className="flex items-center space-x-2 text-sm">
                  <Skeleton className="h-4 flex-1 max-w-xs" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
                {/* Filename */}
                <Skeleton className="h-5 w-48 mt-1" />
              </div>
              {/* External link icon */}
              <Skeleton className="h-4 w-4 flex-shrink-0" />
            </div>

            {/* Repository info - 匹配实际的 flex 布局 */}
            <div className="flex items-center space-x-4 text-sm">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-12" />
            </div>

            {/* Code matches - 匹配实际的代码块布局 */}
            <div className="space-y-1 bg-muted/30 rounded-lg p-3">
              {Array.from({ length: 3 }).map((_, lineIndex) => (
                <div key={lineIndex} className="flex text-sm">
                  {/* Line number */}
                  <Skeleton className="h-4 w-12 flex-shrink-0 mr-3" />
                  {/* Code content */}
                  <div className="flex-1 min-w-0">
                    <Skeleton className={`h-4 ${
                      lineIndex === 0 ? 'w-full' :
                      lineIndex === 1 ? 'w-5/6' : 'w-4/5'
                    }`} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}

export function SearchPageSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header - 匹配 SearchContainer 的 header 布局 */}
      <header className="sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Skeleton className="h-8 w-16 flex-shrink-0" />
            {/* Search input */}
            <div className="flex-1 max-w-2xl mx-8">
              <Skeleton className="h-12 w-full rounded-lg" />
            </div>
            {/* Spacer */}
            <div className="w-16 flex-shrink-0" />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="flex gap-6">
          {/* Sidebar - 匹配 FilterSidebar 的布局 */}
          <aside className="w-64 flex-shrink-0">
            <div className="space-y-6">
              {/* Languages filter */}
              <div>
                <Skeleton className="h-5 w-20 mb-3" />
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>

              {/* Repositories filter */}
              <div>
                <Skeleton className="h-5 w-24 mb-3" />
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>
            </div>
          </aside>

          {/* Main content */}
          <main className="flex-1 min-w-0">
            <LoadingSkeleton />
          </main>
        </div>
      </div>
    </div>
  );
}
