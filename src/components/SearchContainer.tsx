'use client';

import { useSearchParams } from 'next/navigation';
import useSWR from 'swr';
import { Logo } from '@/components/Logo';
import { SearchInput } from '@/components/SearchInput';
import { FilterSidebar } from '@/components/FilterSidebar';
import { ResultCard } from '@/components/ResultCard';
import { LoadingSkeleton } from '@/components/LoadingSkeleton';
import { AlertCircle } from 'lucide-react';
import { searchCode, SearchResponse } from '@/lib/searchService';

const fetcher = async (searchParams: URLSearchParams): Promise<SearchResponse> => {
  return searchCode(searchParams);
};

export function SearchContainer() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q');

  // Use search params as SWR key
  const swrKey = query ? searchParams : null;

  const { data, error, isLoading } = useSWR(swrKey, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 5000,
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Logo size="sm" className="flex-shrink-0" />
            <div className="flex-1 max-w-2xl mx-8">
              <SearchInput 
                size="md" 
                placeholder="Search code..."
                className="w-full"
              />
            </div>
            <div className="w-16 flex-shrink-0" />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="flex gap-6">
          {/* Sidebar */}
          <FilterSidebar className="w-64 flex-shrink-0" />

          {/* Main content */}
          <main className="flex-1 min-w-0">
            {!query ? (
              <EmptyState />
            ) : (
              <SearchResults 
                data={data}
                error={error}
                isLoading={isLoading}
                query={query}
              />
            )}
          </main>
        </div>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="text-center py-12">
      <div className="space-y-4">
        <div className="text-6xl">🔍</div>
        <h2 className="text-xl font-semibold text-foreground">
          Enter a search term to get started
        </h2>
        <p className="text-muted-foreground">
          Search through millions of code repositories to find what you need
        </p>
      </div>
    </div>
  );
}

interface SearchResultsProps {
  data?: SearchResponse;
  error?: Error;
  isLoading: boolean;
  query: string;
}

function SearchResults({ data, error, isLoading, query }: SearchResultsProps) {
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
          <h2 className="text-xl font-semibold text-foreground">
            Something went wrong
          </h2>
          <p className="text-muted-foreground">
            {error.message || 'Failed to load search results. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading && !data) {
    return <LoadingSkeleton />;
  }

  if (!data || data.results.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="space-y-4">
          <div className="text-6xl">📭</div>
          <h2 className="text-xl font-semibold text-foreground">
            No results found
          </h2>
          <p className="text-muted-foreground">
            Try adjusting your search terms or filters
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity duration-200`}>
      {/* Results header */}
      <div className="flex items-center justify-between pb-4 border-b border-border">
        <div>
          <h2 className="text-lg font-semibold text-foreground">
            {data.total.toLocaleString()} results
          </h2>
          <p className="text-sm text-muted-foreground">
            Found in {data.took}ms for &ldquo;{query}&rdquo;
          </p>
        </div>
      </div>

      {/* Results list */}
      <div className="space-y-4">
        {data.results.map((result) => (
          <ResultCard 
            key={result.id} 
            result={result} 
            searchQuery={query}
          />
        ))}
      </div>

      {/* Load more indicator */}
      {isLoading && data && (
        <div className="text-center py-4">
          <div className="text-sm text-muted-foreground">Loading more results...</div>
        </div>
      )}
    </div>
  );
}
