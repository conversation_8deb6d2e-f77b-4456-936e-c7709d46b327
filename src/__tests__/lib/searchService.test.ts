import { searchCode } from '@/lib/searchService';

describe('searchService', () => {
  it('returns empty results when no query is provided', async () => {
    const params = new URLSearchParams();
    const result = await searchCode(params);
    
    expect(result.results).toEqual([]);
    expect(result.total).toBe(0);
    expect(result.query).toBe('');
    expect(result.took).toBe(0);
  });

  it('returns filtered results based on query', async () => {
    const params = new URLSearchParams({ q: 'useState' });
    const result = await searchCode(params);
    
    expect(result.results.length).toBeGreaterThan(0);
    expect(result.query).toBe('useState');
    expect(result.took).toBeGreaterThan(0);
    
    // Check that results contain the search term
    const hasMatchingContent = result.results.some(r => 
      r.matches.some(m => 
        m.content.toLowerCase().includes('usestate')
      )
    );
    expect(hasMatchingContent).toBe(true);
  });

  it('filters results by language', async () => {
    const params = new URLSearchParams({ 
      q: 'useState',
      lang: 'TypeScript'
    });
    const result = await searchCode(params);
    
    // All results should be TypeScript
    const allTypeScript = result.results.every(r => 
      r.repository.language === 'TypeScript'
    );
    expect(allTypeScript).toBe(true);
  });

  it('filters results by repository', async () => {
    const params = new URLSearchParams({ 
      q: 'useState',
      repo: 'facebook/react'
    });
    const result = await searchCode(params);
    
    // All results should be from facebook/react
    const allFromRepo = result.results.every(r => 
      r.repository.name === 'facebook/react'
    );
    expect(allFromRepo).toBe(true);
  });

  it('highlights search terms in results', async () => {
    const params = new URLSearchParams({ q: 'useState' });
    const result = await searchCode(params);
    
    // Check that highlighted content contains <mark> tags
    const hasHighlighting = result.results.some(r => 
      r.matches.some(m => 
        m.highlighted && m.highlighted.includes('<mark>')
      )
    );
    expect(hasHighlighting).toBe(true);
  });

  it('simulates realistic response times', async () => {
    const startTime = Date.now();
    const params = new URLSearchParams({ q: 'test' });
    await searchCode(params);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    expect(duration).toBeGreaterThan(200); // At least 200ms
    expect(duration).toBeLessThan(1000); // Less than 1 second
  });
});
