import { render, screen } from '@testing-library/react';
import { Logo } from '@/components/Logo';

describe('Logo Component', () => {
  it('renders the logo with correct text', () => {
    render(<Logo />);
    
    expect(screen.getByText('g')).toBeInTheDocument();
    expect(screen.getByText('_')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<Logo size="sm" />);
    expect(screen.getByText('g').parentElement).toHaveClass('text-xl');

    rerender(<Logo size="md" />);
    expect(screen.getByText('g').parentElement).toHaveClass('text-2xl');

    rerender(<Logo size="lg" />);
    expect(screen.getByText('g').parentElement).toHaveClass('text-4xl');
  });

  it('applies animation class when animated prop is true', () => {
    render(<Logo animated />);
    expect(screen.getByText('_')).toHaveClass('logo-cursor');
  });

  it('does not apply animation class when animated prop is false', () => {
    render(<Logo animated={false} />);
    expect(screen.getByText('_')).not.toHaveClass('logo-cursor');
  });

  it('applies custom className', () => {
    render(<Logo className="custom-class" />);
    expect(screen.getByText('g').parentElement).toHaveClass('custom-class');
  });
});
