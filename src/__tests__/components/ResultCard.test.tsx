import { render, screen } from '@testing-library/react';
import { ResultCard, SearchResult } from '@/components/ResultCard';

const mockResult: SearchResult = {
  id: '1',
  filename: 'test.ts',
  path: 'src/test.ts',
  repository: {
    name: 'test/repo',
    url: 'https://github.com/test/repo',
    stars: 1000,
    language: 'TypeScript'
  },
  matches: [
    {
      lineNumber: 10,
      content: 'function test() { return true; }',
      highlighted: 'function <mark>test</mark>() { return true; }'
    },
    {
      lineNumber: 15,
      content: 'const result = test();',
      highlighted: 'const result = <mark>test</mark>();'
    }
  ],
  url: 'https://github.com/test/repo/blob/main/src/test.ts#L10'
};

describe('ResultCard Component', () => {
  it('renders result information correctly', () => {
    render(<ResultCard result={mockResult} searchQuery="test" />);
    
    expect(screen.getByText('test.ts')).toBeInTheDocument();
    expect(screen.getByText('src/test.ts')).toBeInTheDocument();
    expect(screen.getByText('test/repo')).toBeInTheDocument();
    expect(screen.getByText('1.0k')).toBeInTheDocument();
    expect(screen.getByText('TypeScript')).toBeInTheDocument();
  });

  it('renders code matches with line numbers', () => {
    render(<ResultCard result={mockResult} searchQuery="test" />);
    
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  it('renders highlighted content when available', () => {
    render(<ResultCard result={mockResult} searchQuery="test" />);
    
    // Check that highlighted content is rendered (contains <mark> tags)
    const highlightedElements = screen.getAllByText(/test/);
    expect(highlightedElements.length).toBeGreaterThan(0);
  });

  it('shows additional matches count when there are more than 3 matches', () => {
    const resultWithManyMatches = {
      ...mockResult,
      matches: [
        ...mockResult.matches,
        { lineNumber: 20, content: 'another test line' },
        { lineNumber: 25, content: 'yet another test line' }
      ]
    };

    render(<ResultCard result={resultWithManyMatches} searchQuery="test" />);
    
    expect(screen.getByText('+1 more matches')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ResultCard result={mockResult} searchQuery="test" className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });
});
